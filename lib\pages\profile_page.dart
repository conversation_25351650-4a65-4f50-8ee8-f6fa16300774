import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/auth_provider.dart';
import '../providers/profile_provider.dart';
import '../models/user_model.dart';

/// 时间范围选项配置类
class TimeRangeOption {
  final String value;
  final String label;
  final String description;
  final IconData icon;

  const TimeRangeOption({
    required this.value,
    required this.label,
    required this.description,
    required this.icon,
  });
}

/// 个人界面页面
class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  static final _avatarRadius = 32.0; // 头像半径
  static final _cardPadding = 16.0; // 卡片内边距
  // 时间范围选项配置
  final List<TimeRangeOption> _timeRangeOptions = [
    TimeRangeOption(
      value: '最近7天',
      label: '最近7天',
      description: '一周内的数据',
      icon: Icons.calendar_view_week_rounded,
    ),
    TimeRangeOption(
      value: '最近14天',
      label: '最近14天',
      description: '两周内的数据',
      icon: Icons.calendar_view_month_rounded,
    ),
    TimeRangeOption(
      value: '最近30天',
      label: '最近30天',
      description: '一个月内的数据',
      icon: Icons.calendar_month_rounded,
    ),
    TimeRangeOption(
      value: '最近60天',
      label: '最近60天',
      description: '两个月内的数据',
      icon: Icons.date_range_rounded,
    ),
  ];
  String _selectedTimeRange = '最近7天';

  @override
  void initState() {
    super.initState();
    // 组件初始化时加载默认数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 同步最新用户资料（使用本地缓存兜底）
      ref.read(authProvider.notifier).syncProfile();
      _loadJourneyStatistics();
    });
  }

  /// 加载旅程统计数据
  void _loadJourneyStatistics() {
    final authState = ref.read(authProvider);
    if (authState.user?.uid != null) {
      ref
          .read(journeyStatisticsProvider.notifier)
          .fetchByTimeRange(uid: authState.user!.uid, timeRange: _selectedTimeRange);
    }
  }

  /// 根据数据点数量决定是否显示点标记
  bool _shouldShowDots(List<double> data) {
    return data.length <= 14; // 14天以内显示点标记
  }

  /// 根据数据点数量调整点标记半径
  double _getDotRadius(List<double> data) {
    if (data.length <= 7) {
      return 4.0; // 7天：较大的点
    } else if (data.length <= 14) {
      return 3.0; // 14天：中等大小的点
    } else {
      return 2.0; // 30天以上：较小的点
    }
  }

  /// 根据数据点数量调整线条宽度
  double _getLineWidth(List<double> data) {
    if (data.length <= 7) {
      return 3.0; // 7天：较粗的线
    } else if (data.length <= 30) {
      return 2.5; // 14-30天：中等粗细
    } else {
      return 2.0; // 60天：较细的线
    }
  }

  /// 计算纵轴的动态范围和刻度间隔
  Map<String, double> _calculateYAxisConfig(List<double> data) {
    if (data.isEmpty) {
      // 数据为空时的默认配置
      return {'minY': 0.0, 'maxY': 10.0, 'interval': 2.0};
    }

    // 过滤掉无效数据
    final validData = data.where((value) => value.isFinite && !value.isNaN).toList();

    if (validData.isEmpty || validData.every((value) => value == 0)) {
      // 所有数据为零或无效时的默认配置
      return {'minY': 0.0, 'maxY': 10.0, 'interval': 2.0};
    }

    final minValue = validData.reduce((a, b) => a < b ? a : b);
    final maxValue = validData.reduce((a, b) => a > b ? a : b);

    // 计算数据范围
    final dataRange = maxValue - minValue;

    // 为了更好的视觉效果，在最大值上方留出一些空间
    final padding = dataRange * 0.1; // 10%的填充
    final adjustedMaxValue = maxValue + padding;

    // 计算合适的最大Y值（向上取整到合适的数值）
    double maxY;
    if (adjustedMaxValue <= 10) {
      maxY = (adjustedMaxValue / 2).ceil() * 2.0; // 以2为单位向上取整
    } else if (adjustedMaxValue <= 50) {
      maxY = (adjustedMaxValue / 5).ceil() * 5.0; // 以5为单位向上取整
    } else if (adjustedMaxValue <= 100) {
      maxY = (adjustedMaxValue / 10).ceil() * 10.0; // 以10为单位向上取整
    } else {
      maxY = (adjustedMaxValue / 20).ceil() * 20.0; // 以20为单位向上取整
    }

    // 确保最大Y值至少比最大数据值大一点
    if (maxY <= maxValue) {
      maxY = maxValue * 1.2;
    }

    // 计算合适的刻度间隔
    double interval;
    if (maxY <= 10) {
      interval = 1.0;
    } else if (maxY <= 20) {
      interval = 2.0;
    } else if (maxY <= 50) {
      interval = 5.0;
    } else if (maxY <= 100) {
      interval = 10.0;
    } else {
      interval = 20.0;
    }

    // 调整间隔以确保有合理数量的刻度线（4-8个）
    final tickCount = maxY / interval;
    if (tickCount > 8) {
      interval = maxY / 6; // 目标6个刻度
      // 将间隔调整为合适的整数
      if (interval <= 1) {
        interval = 1;
      } else if (interval <= 2) {
        interval = 2;
      } else if (interval <= 5) {
        interval = 5;
      } else if (interval <= 10) {
        interval = 10;
      } else if (interval <= 20) {
        interval = 20;
      } else {
        interval = (interval / 10).ceil() * 10.0;
      }
    } else if (tickCount < 4) {
      interval = maxY / 5; // 目标5个刻度
      // 将间隔调整为合适的小数
      if (interval >= 10) {
        interval = (interval / 10).ceil() * 10.0;
      } else if (interval >= 5) {
        interval = 5;
      } else if (interval >= 2) {
        interval = 2;
      } else if (interval >= 1) {
        interval = 1;
      } else {
        interval = 0.5;
      }
    }

    return {
      'minY': 0.0, // 通常从0开始比较直观
      'maxY': maxY,
      'interval': interval,
    };
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    ref.watch(journeyStatisticsProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      body: SingleChildScrollView(
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户信息区域
                _buildUserInfoSection(authState.user),
                const SizedBox(height: 32),

                // 旅程统计区域
                _buildJourneyStatsSection(),
                const SizedBox(height: 32),

                // 设置区域
                _buildSettingsSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建用户信息区域
  Widget _buildUserInfoSection(UserModel? user) {
    return Row(
      children: [
        // 头像
        Container(
          width: _avatarRadius * 2,
          height: _avatarRadius * 2,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey[300],
            border: Border.all(color: Colors.black12, width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                offset: const Offset(0, 2),
                blurRadius: 8,
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipOval(
            child: user?.avatar.isNotEmpty == true
                ? CachedNetworkImage(
                    imageUrl: user!.avatar,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[300],
                      child: Icon(Icons.person, size: 40, color: Colors.grey[600]),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[300],
                      child: Icon(Icons.person, size: 40, color: Colors.grey[600]),
                    ),
                  )
                : Icon(Icons.person, size: 40, color: Colors.grey[600]),
          ),
        ),
        const SizedBox(width: 20),

        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                user?.nickname ?? '未知昵称',
                style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 4),
              Text(
                'UID: ${user?.uid ?? 'unknown'}',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
            ],
          ),
        ),

        // 登出按钮
        _buildLogoutButton(),
      ],
    );
  }

  /// 构建登出按钮
  Widget _buildLogoutButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _showLogoutConfirmDialog,
        borderRadius: BorderRadius.circular(8),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                offset: const Offset(0, 2),
                blurRadius: 4,
              ),
            ],
          ),
          child: const Text(
            '登出',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  /// 显示登出确认对话框
  void _showLogoutConfirmDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
          title: const Text(
            '确认登出',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          content: const Text(
            '您确定要登出当前账户吗？',
            style: TextStyle(fontSize: 16, color: Colors.black87),
          ),
          actions: [
            // 取消按钮
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                '取消',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            // 确认登出按钮
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _performLogout();
              },
              child: const Text(
                '登出',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 执行登出操作
  void _performLogout() async {
    try {
      // 调用AuthProvider的登出方法
      await ref.read(authProvider.notifier).logout();

      // 登出成功后，由于使用了AppEntry组件，AuthProvider状态变化会自动触发页面切换
      // 不需要手动导航，AppEntry会监听authProvider状态并自动显示登录页面
    } catch (e) {
      // 如果登出失败，显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('登出失败：${e.toString()}'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 构建旅程统计区域
  Widget _buildJourneyStatsSection() {
    final journeyState = ref.watch(journeyStatisticsProvider);
    final currentJourneyData = journeyState.chartData;
    final currentJourneyLabels = journeyState.chartLabels;

    // 如果正在加载，显示加载指示器
    if (journeyState.isLoading) {
      return _buildLoadingCard('旅程统计');
    }

    // 如果有错误，显示错误信息
    if (journeyState.error != null) {
      return _buildErrorCard('旅程统计', journeyState.error!, () => _loadJourneyStatistics());
    }

    // 如果没有数据，显示空状态
    if (currentJourneyData.isEmpty) {
      return _buildEmptyCard('旅程统计', '暂无统计数据');
    }

    // 计算动态Y轴配置
    final yAxisConfig = _calculateYAxisConfig(currentJourneyData);
    final minY = yAxisConfig['minY']!;
    final maxY = yAxisConfig['maxY']!;
    final interval = yAxisConfig['interval']!;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black12, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '旅程统计',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
              // 时间范围选择器
              _buildTimeRangeSelector(),
            ],
          ),
          const SizedBox(height: 24),

          // 图表区域 - 添加淡入动画
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 500),
            child: SizedBox(
              key: ValueKey('chart_${journeyState.isLoading}'),
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: true,
                    horizontalInterval: interval,
                    verticalInterval: 1,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(color: Colors.grey[300]!, strokeWidth: 1);
                    },
                    getDrawingVerticalLine: (value) {
                      return FlLine(color: Colors.grey[300]!, strokeWidth: 1);
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 1,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          final index = value.toInt();
                          if (index >= 0 && index < currentJourneyLabels.length) {
                            final label = currentJourneyLabels[index];
                            // 只显示非空标签
                            if (label.isNotEmpty) {
                              return SideTitleWidget(
                                meta: meta,
                                child: Text(
                                  label,
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w400,
                                    fontSize: 12,
                                  ),
                                ),
                              );
                            }
                          }
                          return Container();
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: interval,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          // 根据数值大小决定显示格式
                          String displayText;
                          if (value == value.toInt()) {
                            displayText = value.toInt().toString();
                          } else {
                            displayText = value.toStringAsFixed(1);
                          }
                          return Text(
                            displayText,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w400,
                              fontSize: 12,
                            ),
                          );
                        },
                        reservedSize: 40, // 增加预留空间以适应更大的数字
                      ),
                    ),
                  ),
                  borderData: FlBorderData(show: false),
                  minX: 0,
                  maxX: (currentJourneyData.length - 1).toDouble(),
                  minY: minY,
                  maxY: maxY,
                  lineBarsData: [
                    LineChartBarData(
                      spots: currentJourneyData.asMap().entries.map((e) {
                        return FlSpot(e.key.toDouble(), e.value);
                      }).toList(),
                      isCurved: true,
                      gradient: LinearGradient(
                        colors: [const Color(0xFF64B5F6), const Color(0xFF42A5F5)],
                      ),
                      barWidth: _getLineWidth(currentJourneyData),
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: _shouldShowDots(currentJourneyData),
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: _getDotRadius(currentJourneyData),
                            color: const Color(0xFF1976D2),
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFF64B5F6).withValues(alpha: 0.3),
                            const Color(0xFF42A5F5).withValues(alpha: 0.1),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建时间范围选择器 - 自定义Tab组件
  Widget _buildTimeRangeSelector() {
    final tabOptions = _timeRangeOptions.take(4).toList();
    final selectedIndex = tabOptions.indexWhere(
      (option) => option.value == _selectedTimeRange,
    );
    final currentIndex = selectedIndex >= 0 ? selectedIndex : 0;

    return Container(
      height: 45,
      width: 200,
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: BorderRadius.circular(22),
        border: Border.all(color: Colors.black, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Stack(
        children: [
          // 滑动指示器
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            left: currentIndex * ((200 - 8) / 4) + 4,
            top: 4,
            child: Container(
              width: (200 - 8) / 4,
              height: 35,
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(18),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    offset: const Offset(0, 2),
                    blurRadius: 4,
                    spreadRadius: 0,
                  ),
                ],
              ),
            ),
          ),
          // Tab按钮
          Row(
            children: tabOptions.asMap().entries.map((entry) {
              final index = entry.key;
              final option = entry.value;
              final isSelected = index == currentIndex;

              return Expanded(
                child: GestureDetector(
                  onTap: () {
                    if (option.value != _selectedTimeRange) {
                      setState(() {
                        _selectedTimeRange = option.value;
                      });
                      HapticFeedback.selectionClick();
                      // 切换时间范围时重新加载数据
                      _loadJourneyStatistics();
                    }
                  },
                  child: Container(
                    height: 44,
                    alignment: Alignment.center,
                    child: AnimatedDefaultTextStyle(
                      duration: const Duration(milliseconds: 200),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected ? Colors.white : const Color(0xFF666666),
                      ),
                      child: Text(
                        option.label.replaceAll('最近', ''),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建设置区域
  Widget _buildSettingsSection() {
    return Container(
      padding: EdgeInsets.all(_cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black12, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '设置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF333333),
            ),
          ),

          // 设置选项列表 (演示数据)
          _buildSettingItem('个人信息', Icons.person_outline, () {}),
          _buildSettingItem('通知设置', Icons.notifications_outlined, () {}),
          _buildSettingItem('帮助与反馈', Icons.help_outline, () {}),
          _buildSettingItem('关于我们', Icons.info_outline, () {}, isLast: true),
        ],
      ),
    );
  }

  /// 构建设置选项
  Widget _buildSettingItem(
    String title,
    IconData icon,
    Function onTapFunc, {
    bool isLast = false,
  }) {
    final iconSize = 24.0; // 图标大小
    return Container(
      margin: EdgeInsets.only(top: isLast ? 0 : 8),
      child: InkWell(
        onTap: () {
          onTapFunc();
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            // 根据 isLast 参数条件渲染底部边框
            border: isLast
                ? null
                : const Border(bottom: BorderSide(color: Colors.black26, width: 1)),
          ),
          child: Row(
            children: [
              Icon(icon, size: iconSize, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(fontSize: 16, color: Color(0xFF333333)),
                ),
              ),
              Icon(Icons.chevron_right, size: iconSize, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建加载状态卡片 - 骨架屏动画
  Widget _buildLoadingCard(String title) {
    return Container(
      padding: EdgeInsets.all(_cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black12, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
              // 时间范围选择器骨架
              _buildSkeletonTimeRangeSelector(),
            ],
          ),
          const SizedBox(height: 24),

          // 图表骨架动画
          SizedBox(height: 200, child: _buildChartSkeleton()),
        ],
      ),
    );
  }

  /// 构建时间范围选择器骨架
  Widget _buildSkeletonTimeRangeSelector() {
    return _ShimmerContainer(width: 200, height: 45, borderRadius: 22);
  }

  /// 构建图表骨架动画
  Widget _buildChartSkeleton() {
    return Stack(
      children: [
        // 背景网格线
        CustomPaint(
          size: const Size(double.infinity, 200),
          painter: _SkeletonGridPainter(),
        ),

        // 模拟折线图骨架
        Positioned.fill(child: CustomPaint(painter: _SkeletonLinePainter())),

        // 底部X轴标签骨架
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: 30,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              4,
              (index) => _ShimmerContainer(width: 20, height: 12, borderRadius: 6),
            ),
          ),
        ),

        // 左侧Y轴标签骨架
        Positioned(
          left: 0,
          top: 0,
          bottom: 30,
          width: 40,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              5,
              (index) => _ShimmerContainer(width: 15, height: 12, borderRadius: 6),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建错误状态卡片
  Widget _buildErrorCard(String title, String error, VoidCallback onRetry) {
    return Container(
      padding: EdgeInsets.all(_cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black12, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 20),
          Center(
            child: Column(
              children: [
                Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 12),
                Text(
                  error,
                  style: TextStyle(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(onPressed: onRetry, child: const Text('重试')),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态卡片
  Widget _buildEmptyCard(String title, String message) {
    return Container(
      padding: EdgeInsets.all(_cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.black12, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 40),
          Center(
            child: Column(
              children: [
                Icon(Icons.bar_chart, size: 48, color: Colors.grey[300]),
                const SizedBox(height: 12),
                Text(
                  message,
                  style: TextStyle(color: Colors.grey[600]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }
}

/// Shimmer动画容器
class _ShimmerContainer extends StatefulWidget {
  final double width;
  final double height;
  final double borderRadius;

  const _ShimmerContainer({
    required this.width,
    required this.height,
    this.borderRadius = 8,
  });

  @override
  State<_ShimmerContainer> createState() => _ShimmerContainerState();
}

class _ShimmerContainerState extends State<_ShimmerContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              stops: [
                (_animation.value - 1).clamp(0.0, 1.0),
                _animation.value.clamp(0.0, 1.0),
                (_animation.value + 1).clamp(0.0, 1.0),
              ],
              colors: [Colors.grey[300]!, Colors.grey[200]!, Colors.grey[300]!],
            ),
          ),
        );
      },
    );
  }
}

/// 骨架网格线绘制器
class _SkeletonGridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey[200]!
      ..strokeWidth = 1;

    // 绘制水平网格线
    for (int i = 1; i <= 4; i++) {
      final y = (size.height - 30) * i / 5;
      canvas.drawLine(Offset(40, y), Offset(size.width, y), paint);
    }

    // 绘制垂直网格线
    for (int i = 1; i <= 4; i++) {
      final x = 40 + (size.width - 40) * i / 5;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height - 30), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// 骨架折线图绘制器
class _SkeletonLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final linePaint = Paint()
      ..color = Colors.grey[300]!
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final dotPaint = Paint()
      ..color = Colors.grey[300]!
      ..style = PaintingStyle.fill;

    // 模拟数据点
    final points = <Offset>[
      Offset(40 + (size.width - 40) * 0.1, size.height * 0.7),
      Offset(40 + (size.width - 40) * 0.3, size.height * 0.4),
      Offset(40 + (size.width - 40) * 0.5, size.height * 0.6),
      Offset(40 + (size.width - 40) * 0.7, size.height * 0.3),
      Offset(40 + (size.width - 40) * 0.9, size.height * 0.5),
    ];

    // 绘制折线
    final path = Path();
    path.moveTo(points.first.dx, points.first.dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    canvas.drawPath(path, linePaint);

    // 绘制数据点
    for (final point in points) {
      canvas.drawCircle(point, 3, dotPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
