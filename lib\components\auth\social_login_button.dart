import 'package:flutter/material.dart';

/// 第三方登录类型枚举
enum SocialLoginType {
  wechat, // 微信
  qq, // QQ
}

/// 第三方登录按钮组件
class SocialLoginButton extends StatelessWidget {
  /// 登录类型
  final SocialLoginType type;

  /// 点击回调
  final VoidCallback? onPressed;

  /// 按钮尺寸
  final double size;

  /// 是否启用
  final bool enabled;

  const SocialLoginButton({
    super.key,
    required this.type,
    this.onPressed,
    this.size = 48.0,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final config = _getSocialConfig();

    return GestureDetector(
      onTap: enabled ? onPressed : null,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: enabled ? config.backgroundColor : Colors.grey.shade300,
          shape: BoxShape.circle,
          boxShadow: enabled
              ? [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    offset: const Offset(0, 2),
                    blurRadius: 8,
                  ),
                ]
              : null,
        ),
        child: Icon(
          config.icon,
          color: enabled ? config.iconColor : Colors.grey.shade500,
          size: size * 0.5,
        ),
      ),
    );
  }

  /// 获取第三方登录配置
  _SocialConfig _getSocialConfig() {
    switch (type) {
      case SocialLoginType.wechat:
        return const _SocialConfig(
          backgroundColor: Color(0xFF07C160),
          iconColor: Colors.white,
          icon: Icons.wechat,
        );
      case SocialLoginType.qq:
        return const _SocialConfig(
          backgroundColor: Color(0xFF12B7F5),
          iconColor: Colors.white,
          icon: Icons.chat,
        );
    }
  }
}

/// 第三方登录配置
class _SocialConfig {
  final Color backgroundColor;
  final Color iconColor;
  final IconData icon;

  const _SocialConfig({
    required this.backgroundColor,
    required this.iconColor,
    required this.icon,
  });
}

/// 第三方登录按钮组
class SocialLoginButtons extends StatelessWidget {
  /// 微信登录回调
  final VoidCallback? onWechatPressed;

  /// QQ登录回调
  final VoidCallback? onQQPressed;

  /// 按钮尺寸
  final double buttonSize;

  /// 按钮间距
  final double spacing;

  /// 是否启用
  final bool enabled;

  const SocialLoginButtons({
    super.key,
    this.onWechatPressed,
    this.onQQPressed,
    this.buttonSize = 48.0,
    this.spacing = 24.0,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 分割线
        Row(
          children: [
            Expanded(child: Divider(color: Colors.grey.shade300, thickness: 1.0)),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '其他方式登录',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ),
            Expanded(child: Divider(color: Colors.grey.shade300, thickness: 1.0)),
          ],
        ),

        const SizedBox(height: 24),

        // 第三方登录按钮
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SocialLoginButton(
              type: SocialLoginType.wechat,
              onPressed: onWechatPressed,
              size: buttonSize,
              enabled: enabled,
            ),
            SizedBox(width: spacing),
            SocialLoginButton(
              type: SocialLoginType.qq,
              onPressed: onQQPressed,
              size: buttonSize,
              enabled: enabled,
            ),
          ],
        ),
      ],
    );
  }
}
