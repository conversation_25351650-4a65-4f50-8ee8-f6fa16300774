import 'package:chickafocus/components/bottom_navigation_bar.dart';
import 'package:chickafocus/pages/home_page.dart';
import 'package:chickafocus/pages/souvenir_list_page.dart';
import 'package:chickafocus/pages/profile_page.dart';
import 'package:chickafocus/providers/app_provider.dart';
import 'package:chickafocus/providers/auth_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';

class ChickMainPage extends ConsumerStatefulWidget {
  const ChickMainPage({super.key});

  @override
  ConsumerState<ChickMainPage> createState() => _ChickMainPageState();
}

class _ChickMainPageState extends ConsumerState<ChickMainPage> {
  /// PageView 控制器
  /// 用于控制页面切换
  final PageController _pageController = PageController();

  /// 页面
  final List<Widget> _pages = [
    HomePage(),
    Center(child: Text('日程表')),
    SouvenirListPage(),
    ProfilePage(),
  ];

  /// 获取底部导航栏的位置值
  ///
  /// @return (double, double, double) 返回定位值(左, 底部, 右)
  (double, double, double) getPostionValues() {
    // 根据屏幕方向返回定位值
    // 竖屏状态下定位到底部中心
    // 横屏状态下定位到右下角
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (16, 32, 16);
    } else {
      final screenSize = MediaQuery.of(context).size;
      return (screenSize.height - 32, 16, 16);
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final (pl, pb, pr) = getPostionValues();
    final appState = ref.watch(appProvider);
    return Scaffold(
      extendBody: true,
      body: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            itemCount: _pages.length,
            onPageChanged: (index) {
              if (index == 3) {
                // 每次进入 Profile 页面时同步用户资料
                ref.read(authProvider.notifier).syncProfile();
              }
            },
            itemBuilder: (context, index) {
              return _pages[index];
            },
          ),
          appState.when(
            data: (data) {
              return Positioned(
                left: pl,
                right: pr,
                bottom: pb,
                child: AnimatedOpacity(
                  opacity: data.isBottomNavigationBarVisible ? 1.0 : 0.0,
                  duration: const Duration(milliseconds: 300),
                  child: ChickBottomNavigationBar(
                    items: [
                      GestureDetector(
                        // child: Icon(Icons.home),
                        child: SvgPicture.asset(
                          'assets/icons/map.svg',
                          width: 24,
                          height: 24,
                        ),
                        onTap: () => _pageController.jumpToPage(0),
                      ),
                      GestureDetector(
                        child: SvgPicture.asset(
                          'assets/icons/to-do.svg',
                          width: 24,
                          height: 24,
                        ),
                        onTap: () => _pageController.jumpToPage(1),
                      ),
                      GestureDetector(
                        child: SvgPicture.asset(
                          'assets/icons/honor.svg',
                          width: 32,
                          height: 32,
                        ),
                        onTap: () => _pageController.jumpToPage(2),
                      ),
                      GestureDetector(
                        child: SvgPicture.asset(
                          'assets/icons/profile.svg',
                          width: 32,
                          height: 32,
                        ),
                        onTap: () {
                          _pageController.jumpToPage(3);
                          // 点击进入时也触发一次同步
                          ref.read(authProvider.notifier).syncProfile();
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
            error: (error, stackTrace) => const SizedBox.shrink(),
            loading: () => const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}
