// 简化版本的 ApiResponse 类用于演示
class ApiResponse<T> {
  final int code;
  final String message;
  final T? data;

  const ApiResponse({required this.code, required this.message, this.data});

  bool get isSuccess => code == 0 || code == 200;
  bool get isFailure => !isSuccess;

  @override
  String toString() {
    return 'ApiResponse(code: $code, message: $message, data: $data)';
  }
}

void main() {
  print('=== 登录错误处理修复验证 ===\n');

  // 测试1: 验证 ApiResponse 对错误码的处理
  print('测试1: ApiResponse 错误处理');
  testApiResponseErrorHandling();

  print('\n测试2: 自动注册判断逻辑');
  testShouldAutoRegisterLogic();

  print('\n=== 修复总结 ===');
  printFixSummary();
}

void testApiResponseErrorHandling() {
  // 模拟服务器返回的错误响应
  final errorResponse = ApiResponse<String>(code: 20000, message: '用户登录凭据无效', data: null);

  print('服务器响应: ${errorResponse.toString()}');
  print('isSuccess: ${errorResponse.isSuccess}');
  print('isFailure: ${errorResponse.isFailure}');
  print('错误信息: ${errorResponse.message}');

  // 验证成功响应
  final successResponse = ApiResponse<String>(
    code: 0,
    message: 'Success',
    data: 'login_token_123',
  );

  print('\n成功响应: ${successResponse.toString()}');
  print('isSuccess: ${successResponse.isSuccess}');
  print('isFailure: ${successResponse.isFailure}');
}

void testShouldAutoRegisterLogic() {
  // 复制 AuthNotifier 中的逻辑
  bool shouldAutoRegister(String errorMessage, int errorCode) {
    final userNotFoundKeywords = [
      '用户不存在',
      '用户名不存在',
      'user not found',
      'user does not exist',
    ];
    final lowerErrorMessage = errorMessage.toLowerCase();

    // 明确排除凭据无效的情况
    final credentialInvalidKeywords = [
      '用户登录凭据无效',
      '凭据无效',
      '密码错误',
      '用户名或密码错误',
      'invalid credentials',
      'invalid password',
      'wrong password',
    ];

    // 如果是凭据无效，不应该自动注册
    if (credentialInvalidKeywords.any(
      (keyword) => lowerErrorMessage.contains(keyword.toLowerCase()),
    )) {
      return false;
    }

    return userNotFoundKeywords.any(
      (keyword) => lowerErrorMessage.contains(keyword.toLowerCase()),
    );
  }

  // 测试各种错误情况
  final testCases = [
    {'message': '用户登录凭据无效', 'code': 20000, 'expectedAutoRegister': false},
    {'message': '密码错误', 'code': 20001, 'expectedAutoRegister': false},
    {'message': '用户名或密码错误', 'code': 20002, 'expectedAutoRegister': false},
    {'message': '用户不存在', 'code': 40000, 'expectedAutoRegister': true},
    {'message': '用户名不存在', 'code': 40001, 'expectedAutoRegister': true},
    {'message': 'user not found', 'code': 40002, 'expectedAutoRegister': true},
    {'message': '网络连接失败', 'code': 50000, 'expectedAutoRegister': false},
  ];

  for (final testCase in testCases) {
    final message = testCase['message'] as String;
    final code = testCase['code'] as int;
    final expected = testCase['expectedAutoRegister'] as bool;

    final result = shouldAutoRegister(message, code);
    final status = result == expected ? '✅ 通过' : '❌ 失败';

    print('错误信息: "$message" -> 自动注册: $result (期望: $expected) $status');
  }
}

void printFixSummary() {
  print('''
修复内容:
1. ✅ 修改了 HTTP 客户端的响应拦截器
   - 移除了业务错误码的自动异常抛出
   - 让业务层通过 ApiResponse.isSuccess 来判断成功/失败

2. ✅ 优化了 AuthProvider 中的 _shouldAutoRegister 方法
   - 明确排除"用户登录凭据无效"等凭据错误
   - 只有在明确的"用户不存在"错误时才自动注册

3. ✅ 简化了异常处理逻辑
   - 网络异常直接显示错误，不尝试自动注册
   - 确保错误信息能正确传递到UI层

4. ✅ 登录页面的错误显示机制保持不变
   - 通过 LoginFormProvider 的 generalError 显示错误
   - 用户输入时自动清除错误信息

修复结果:
- 当用户输入错误的账号密码时，会显示"用户登录凭据无效"
- 不会触发自动注册流程
- 错误信息会正确显示在登录页面上
- 用户可以重新输入正确的凭据
''');
}
