import 'dart:math';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../api/network/http_client.dart';
import '../api/services/auth_service.dart';
import '../models/user_model.dart';
import 'storage_provider.dart';
import '../services/local_storage_service.dart';

/// 认证服务Provider
final authServiceProvider = Provider<AuthService>((ref) {
  final httpClient = ref.watch(httpClientProvider);
  return AuthService(httpClient);
});

/// 当前用户状态
class AuthState {
  final UserModel? user;
  final String? token;
  final bool isLoading;
  final String? error;

  const AuthState({this.user, this.token, this.isLoading = false, this.error});

  AuthState copyWith({UserModel? user, String? token, bool? isLoading, String? error}) {
    return AuthState(
      user: user ?? this.user,
      token: token ?? this.token,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  bool get isAuthenticated => user != null && token != null;

  @override
  String toString() {
    return 'AuthState(user: $user, isAuthenticated: $isAuthenticated, isLoading: $isLoading, error: $error)';
  }
}

/// 认证状态Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  final httpClient = ref.watch(httpClientProvider);
  final storage = ref.watch(localStorageProvider);
  return AuthNotifier(authService, httpClient, storage)..initializeFromStorage();
});

/// 认证状态管理器
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;
  final HttpClient _httpClient;
  final UserStorageService _storage;

  AuthNotifier(this._authService, this._httpClient, this._storage)
    : super(const AuthState());

  /// 应用启动时，从本地恢复 token 和用户信息
  Future<void> initializeFromStorage() async {
    final token = await _storage.readToken();
    final user = await _storage.readUser();
    if (token != null) {
      _httpClient.setToken(token);
    }
    if (token != null || user != null) {
      state = state.copyWith(user: user, token: token);
    }
  }

  /// 用户登录
  Future<void> login(String credentialIdentifier, String credentialSecret) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = LoginRequest(
        credentialType: 'password',
        credentialIdentifier: credentialIdentifier,
        credentialSecret: credentialSecret,
      );

      final response = await _authService.login(request);

      if (response.isSuccess && response.data != null) {
        final authData = response.data!;
        _httpClient.setToken(authData.token);
        // 持久化
        await _storage.saveToken(authData.token);
        await _storage.saveUser(authData.user);

        state = state.copyWith(
          user: authData.user,
          token: authData.token,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 登录或自动注册
  ///
  /// 如果登录失败且错误表明用户不存在，则自动注册新用户
  Future<void> loginOrAutoRegister(
    String credentialIdentifier,
    String credentialSecret,
  ) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // 首先尝试登录
      final loginRequest = LoginRequest(
        credentialType: 'password',
        credentialIdentifier: credentialIdentifier,
        credentialSecret: credentialSecret,
      );

      final loginResponse = await _authService.login(loginRequest);

      // 调试信息
      print(
        '🔍 AuthProvider: 登录响应 - code: ${loginResponse.code}, message: ${loginResponse.message}, isSuccess: ${loginResponse.isSuccess}',
      );

      if (loginResponse.isSuccess && loginResponse.data != null) {
        // 登录成功
        final authData = loginResponse.data!;
        _httpClient.setToken(authData.token);
        await _storage.saveToken(authData.token);
        await _storage.saveUser(authData.user);

        state = state.copyWith(
          user: authData.user,
          token: authData.token,
          isLoading: false,
        );
        return;
      }

      // 登录失败，检查是否是用户不存在的错误
      if (_shouldAutoRegister(
        errorMessage: loginResponse.message,
        errorCode: loginResponse.code,
      )) {
        // 自动注册新用户
        await _performAutoRegister(credentialIdentifier, credentialSecret);
      } else {
        // 其他登录错误
        print('🔍 AuthProvider: 设置错误状态 - ${loginResponse.message}');
        state = state.copyWith(isLoading: false, error: loginResponse.message);
        print(
          '🔍 AuthProvider: 当前状态 - error: ${state.error}, isLoading: ${state.isLoading}',
        );
      }
    } catch (e) {
      // 网络或其他异常，直接显示错误，不尝试自动注册
      state = state.copyWith(isLoading: false, error: '登录失败：${e.toString()}');
    }
  }

  /// 判断是否应该自动注册
  bool _shouldAutoRegister({required String errorMessage, required int errorCode}) {
    print(
      '🔍 AuthProvider: 检查是否需要自动注册 - errorMessage: "$errorMessage", errorCode: $errorCode',
    );

    // 根据后端返回的错误信息判断是否是用户不存在
    // 只有在明确表示用户不存在的情况下才自动注册
    final userNotFoundKeywords = [
      '用户不存在',
      '用户名不存在',
      'user not found',
      'user does not exist',
    ];
    final lowerErrorMessage = errorMessage.toLowerCase();

    // 排除凭据无效的情况
    final credentialInvalidKeywords = ['用户登录凭据无效', '凭据无效'];

    if (credentialInvalidKeywords.any(
      (keyword) => lowerErrorMessage.contains(keyword.toLowerCase()),
    )) {
      print('🔍 AuthProvider: 检测到凭据无效错误，不自动注册');
      return false;
    }

    final shouldAutoRegister = userNotFoundKeywords.any(
      (keyword) => lowerErrorMessage.contains(keyword.toLowerCase()),
    );

    print('🔍 AuthProvider: 自动注册判断结果 - $shouldAutoRegister');
    return shouldAutoRegister;
  }

  /// 执行自动注册
  Future<void> _performAutoRegister(
    String credentialIdentifier,
    String credentialSecret,
  ) async {
    // 生成默认昵称
    final nickname = '用户 ${Random().nextInt(899999) + 100000}';

    final registerRequest = RegisterRequest(
      nickname: nickname,
      credentialType: 'password',
      credentialIdentifier: credentialIdentifier,
      credentialSecret: credentialSecret,
    );

    final registerResponse = await _authService.register(registerRequest);

    if (registerResponse.isSuccess && registerResponse.data != null) {
      // 注册成功，自动登录
      final authData = registerResponse.data!;
      _httpClient.setToken(authData.token);
      await _storage.saveToken(authData.token);
      await _storage.saveUser(authData.user);

      state = state.copyWith(
        user: authData.user,
        token: authData.token,
        isLoading: false,
      );
    } else {
      state = state.copyWith(
        isLoading: false,
        error: '自动注册失败：${registerResponse.message}',
      );
    }
  }

  /// 用户注册
  Future<void> register({
    required String nickname,
    required String credentialIdentifier,
    required String credentialSecret,
    String? avatar,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = RegisterRequest(
        nickname: nickname,
        avatar: avatar,
        credentialType: 'password',
        credentialIdentifier: credentialIdentifier,
        credentialSecret: credentialSecret,
      );

      final response = await _authService.register(request);

      if (response.isSuccess && response.data != null) {
        final authData = response.data!;
        _httpClient.setToken(authData.token);
        // 持久化
        await _storage.saveToken(authData.token);
        await _storage.saveUser(authData.user);

        state = state.copyWith(
          user: authData.user,
          token: authData.token,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 更新用户信息
  Future<void> updateUser({required String nickname, required String avatar}) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = UserUpdateRequest(nickname: nickname, avatar: avatar);

      final response = await _authService.updateUser(request);

      if (response.isSuccess && response.data != null) {
        final authData = response.data!;
        _httpClient.setToken(authData.token);
        // 持久化
        await _storage.saveToken(authData.token);
        await _storage.saveUser(authData.user);

        state = state.copyWith(
          user: authData.user,
          token: authData.token,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 同步服务器的个人资料，并与本地对比更新
  Future<void> syncProfile() async {
    final token = _httpClient.token;
    if (token == null) return; // 未登录不拉取

    try {
      final response = await _authService.getProfile();
      if (response.isSuccess && response.data != null) {
        final remote = response.data!;
        final local = await _storage.readUser();
        // 对比：如果任意字段不同，则更新本地与状态
        final needUpdate =
            local == null ||
            local.id != remote.id ||
            local.updatedAt != remote.updatedAt ||
            local.nickname != remote.nickname ||
            local.avatar != remote.avatar ||
            local.uid != remote.uid;
        if (needUpdate) {
          await _storage.saveUser(remote);
          state = state.copyWith(user: remote);
        }
      }
    } catch (e) {
      // 失败保持本地缓存显示，不抛错给 UI
    }
  }

  /// 登出
  Future<void> logout() async {
    _httpClient.clearToken();
    await _storage.deleteToken();
    await _storage.deleteUser();
    state = const AuthState();
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}
