import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:chickafocus/pages/login_page.dart';
import 'package:chickafocus/providers/login_form_provider.dart';

void main() {
  group('Login Error Display Tests', () {
    testWidgets('Login form should display general error when set', (WidgetTester tester) async {
      // 创建一个测试应用
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const LoginPage(),
          ),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 验证初始状态下没有错误显示
      expect(find.text('用户登录凭据无效'), findsNothing);

      // 模拟设置错误信息
      final container = ProviderScope.containerOf(
        tester.element(find.byType(LoginPage)),
      );
      
      container.read(loginFormProvider.notifier).setGeneralError('用户登录凭据无效');
      
      // 重新构建页面
      await tester.pumpAndSettle();

      // 验证错误信息是否显示
      expect(find.text('用户登录凭据无效'), findsOneWidget);
      
      // 验证错误容器的样式
      final errorContainer = find.byType(Container).evaluate()
          .where((element) {
            final container = element.widget as Container;
            final decoration = container.decoration as BoxDecoration?;
            return decoration?.color?.value == const Color(0xFFF44336).withValues(alpha: 0.1).value;
          });
      
      expect(errorContainer.length, greaterThan(0));
    });

    testWidgets('Login form should clear error when user types', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const LoginPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 设置错误信息
      final container = ProviderScope.containerOf(
        tester.element(find.byType(LoginPage)),
      );
      
      container.read(loginFormProvider.notifier).setGeneralError('用户登录凭据无效');
      await tester.pumpAndSettle();

      // 验证错误信息显示
      expect(find.text('用户登录凭据无效'), findsOneWidget);

      // 在用户名输入框中输入内容
      final usernameField = find.byType(TextField).first;
      await tester.enterText(usernameField, 'testuser');
      await tester.pumpAndSettle();

      // 验证错误信息被清除
      expect(find.text('用户登录凭据无效'), findsNothing);
    });

    test('LoginFormNotifier should handle error states correctly', () {
      final notifier = LoginFormNotifier();
      
      // 初始状态
      expect(notifier.state.generalError, isNull);
      
      // 设置错误
      notifier.setGeneralError('用户登录凭据无效');
      expect(notifier.state.generalError, '用户登录凭据无效');
      
      // 更新用户名应该清除错误
      notifier.updateUsername('testuser');
      expect(notifier.state.generalError, isNull);
      
      // 重新设置错误
      notifier.setGeneralError('网络连接失败');
      expect(notifier.state.generalError, '网络连接失败');
      
      // 更新密码应该清除错误
      notifier.updatePassword('password123');
      expect(notifier.state.generalError, isNull);
      
      // 重新设置错误
      notifier.setGeneralError('服务器错误');
      expect(notifier.state.generalError, '服务器错误');
      
      // 清除所有错误
      notifier.clearErrors();
      expect(notifier.state.generalError, isNull);
    });
  });
}
