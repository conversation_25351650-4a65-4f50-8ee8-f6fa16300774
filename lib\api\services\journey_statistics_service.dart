import '../network/http_client.dart';
import '../../models/api_response.dart';
import '../../models/journey_statistics_model.dart';

/// 旅程统计服务
/// 
/// 负责处理与旅程统计相关的API请求
class JourneyStatisticsService {
  final HttpClient _httpClient;

  const JourneyStatisticsService(this._httpClient);

  /// 获取旅程统计数据
  /// 
  /// [request] 统计请求参数，包含用户ID、时间范围和统计粒度
  /// 返回统计数据列表的API响应
  Future<ApiResponse<List<JourneyStatisticsModel>>> getJourneyStatistics(
    JourneyStatisticsRequest request,
  ) async {
    try {
      final response = await _httpClient.get<Map<String, dynamic>>(
        '/journey/statistics',
        queryParameters: request.toQueryParams(),
      );

      final apiResponse = ApiResponse<List<JourneyStatisticsModel>>.fromJson(
        response.data!,
        (data) {
          if (data is List) {
            return data
                .map((item) => JourneyStatisticsModel.fromJson(item as Map<String, dynamic>))
                .toList();
          }
          return <JourneyStatisticsModel>[];
        },
      );

      return apiResponse;
    } catch (e) {
      return ApiResponse<List<JourneyStatisticsModel>>(
        code: -1,
        message: '获取旅程统计失败: $e',
        data: null,
      );
    }
  }

  /// 根据时间范围和粒度构建统计请求
  /// 
  /// [uid] 用户ID
  /// [days] 天数
  /// [granularity] 统计粒度
  /// [endDate] 结束日期，默认为今天
  JourneyStatisticsRequest buildRequest({
    required String uid,
    required int days,
    required StatisticsGranularity granularity,
    DateTime? endDate,
  }) {
    final end = endDate ?? DateTime.now();
    final start = end.subtract(Duration(days: days - 1));

    return JourneyStatisticsRequest(
      uid: uid,
      startDate: _formatDate(start),
      endDate: _formatDate(end),
      granularity: granularity.value,
    );
  }

  /// 格式化日期为API要求的格式 (YYYY-MM-DD)
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}