import 'dart:io';

import 'package:chickafocus/pages/main_page.dart';
import 'package:chickafocus/pages/login_page.dart';
import 'package:chickafocus/providers/auth_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await initialize();

  runApp(ProviderScope(child: const ChickApp()));
}

Future<void> initialize() async {
  // 沉浸式状态栏
  if (Platform.isAndroid) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
      ),
    );
  }

  // 初始化 Hive 并打开用户 Box
  await Hive.initFlutter();
  await Hive.openBox('userBox');
}

class ChickApp extends StatelessWidget {
  const ChickApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '小鸡旅行',
      theme: ThemeData(colorScheme: ColorScheme.fromSeed(seedColor: Colors.white)),
      home: const AppEntry(),
    );
  }
}

/// 应用入口组件
/// 根据登录状态决定显示登录页面还是主页面
class AppEntry extends ConsumerWidget {
  const AppEntry({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);

    // 根据认证状态返回相应页面
    if (authState.isAuthenticated) {
      return const ChickMainPage();
    } else {
      return const LoginPage();
    }
  }
}
