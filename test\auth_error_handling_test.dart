import 'package:flutter_test/flutter_test.dart';
import 'package:chickafocus/models/api_response.dart';
import 'package:chickafocus/providers/auth_provider.dart';

void main() {
  group('Auth Error Handling Tests', () {
    test('ApiResponse should correctly identify success and failure', () {
      // 测试成功响应
      final successResponse = ApiResponse<String>(
        code: 0,
        message: 'Success',
        data: 'test data',
      );
      expect(successResponse.isSuccess, true);
      expect(successResponse.isFailure, false);

      final successResponse200 = ApiResponse<String>(
        code: 200,
        message: 'Success',
        data: 'test data',
      );
      expect(successResponse200.isSuccess, true);
      expect(successResponse200.isFailure, false);

      // 测试失败响应（用户登录凭据无效）
      final failureResponse = ApiResponse<String>(
        code: 20000,
        message: '用户登录凭据无效',
        data: null,
      );
      expect(failureResponse.isSuccess, false);
      expect(failureResponse.isFailure, true);
      expect(failureResponse.message, '用户登录凭据无效');
    });

    test('_shouldAutoRegister should correctly identify credential errors', () {
      // 创建一个测试用的 AuthNotifier 实例来测试私有方法
      // 注意：这里我们需要通过反射或者将方法设为公共来测试
      // 为了简化，我们直接测试逻辑

      // 测试凭据无效的情况 - 不应该自动注册
      final credentialInvalidMessages = [
        '用户登录凭据无效',
        '凭据无效',
        '密码错误',
        '用户名或密码错误',
        'invalid credentials',
        'invalid password',
        'wrong password',
      ];

      for (final message in credentialInvalidMessages) {
        final shouldAutoRegister = _testShouldAutoRegister(
          errorMessage: message,
          errorCode: 20000,
        );
        expect(shouldAutoRegister, false, 
          reason: 'Should not auto-register for credential error: $message');
      }

      // 测试用户不存在的情况 - 应该自动注册
      final userNotFoundMessages = [
        '用户不存在',
        '用户名不存在',
        'user not found',
        'user does not exist',
      ];

      for (final message in userNotFoundMessages) {
        final shouldAutoRegister = _testShouldAutoRegister(
          errorMessage: message,
          errorCode: 40000,
        );
        expect(shouldAutoRegister, true,
          reason: 'Should auto-register for user not found error: $message');
      }
    });
  });
}

// 复制 AuthNotifier 中的 _shouldAutoRegister 逻辑用于测试
bool _testShouldAutoRegister({required String errorMessage, required int errorCode}) {
  final userNotFoundKeywords = [
    '用户不存在',
    '用户名不存在', 
    'user not found',
    'user does not exist',
  ];
  final lowerErrorMessage = errorMessage.toLowerCase();

  // 明确排除凭据无效的情况，这种情况应该提示用户而不是自动注册
  final credentialInvalidKeywords = [
    '用户登录凭据无效',
    '凭据无效',
    '密码错误',
    '用户名或密码错误',
    'invalid credentials',
    'invalid password',
    'wrong password',
  ];
  
  // 如果是凭据无效，不应该自动注册
  if (credentialInvalidKeywords.any(
    (keyword) => lowerErrorMessage.contains(keyword.toLowerCase()),
  )) {
    return false;
  }

  return userNotFoundKeywords.any(
    (keyword) => lowerErrorMessage.contains(keyword.toLowerCase()),
  );
}
