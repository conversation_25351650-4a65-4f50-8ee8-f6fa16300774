import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../api/network/http_client.dart';
import '../api/services/journey_statistics_service.dart';
import '../models/journey_statistics_model.dart';

final journeyStatisticsServiceProvider = Provider<JourneyStatisticsService>((ref) {
  final httpClient = ref.watch(httpClientProvider);
  return JourneyStatisticsService(httpClient);
});

class JourneyStatisticsState {
  final List<JourneyStatisticsModel> data;
  final bool isLoading;
  final String? error;

  const JourneyStatisticsState({
    this.data = const [],
    this.isLoading = false,
    this.error,
  });

  JourneyStatisticsState copyWith({
    List<JourneyStatisticsModel>? data,
    bool? isLoading,
    String? error,
  }) {
    return JourneyStatisticsState(
      data: data ?? this.data,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  List<double> get chartData {
    return data.map((item) => item.numericValue).toList();
  }

  List<String> get chartLabels {
    return data.map((item) {
      final dateTime = item.dateTime;
      if (dateTime == null) return item.date;

      final now = DateTime.now();
      final isToday =
          dateTime.year == now.year &&
          dateTime.month == now.month &&
          dateTime.day == now.day;

      if (isToday) {
        return '�)';
      } else {
        return '${dateTime.month}/${dateTime.day}';
      }
    }).toList();
  }

  @override
  String toString() {
    return 'JourneyStatisticsState(data: ${data.length} items, isLoading: $isLoading, error: $error)';
  }
}

final journeyStatisticsProvider =
    StateNotifierProvider<JourneyStatisticsNotifier, JourneyStatisticsState>((ref) {
      final service = ref.watch(journeyStatisticsServiceProvider);
      return JourneyStatisticsNotifier(service);
    });

class JourneyStatisticsNotifier extends StateNotifier<JourneyStatisticsState> {
  final JourneyStatisticsService _service;

  JourneyStatisticsNotifier(this._service) : super(const JourneyStatisticsState());

  Future<void> fetchJourneyStatistics({
    required String uid,
    required int days,
    StatisticsGranularity granularity = StatisticsGranularity.day,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final request = _service.buildRequest(
        uid: uid,
        days: days,
        granularity: granularity,
      );

      final response = await _service.getJourneyStatistics(request);

      if (response.isSuccess && response.data != null) {
        state = state.copyWith(data: response.data!, isLoading: false);
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: '发生错误: $e');
    }
  }

  Future<void> fetchByTimeRange({required String uid, required String timeRange}) async {
    final days = _parseTimeRangeToDays(timeRange);
    final granularity = _getGranularityByDays(days);

    await fetchJourneyStatistics(uid: uid, days: days, granularity: granularity);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  int _parseTimeRangeToDays(String timeRange) {
    switch (timeRange) {
      case '7':
        return 7;
      case '14':
        return 14;
      case '30':
        return 30;
      case '60':
        return 60;
      default:
        return 7;
    }
  }

  StatisticsGranularity _getGranularityByDays(int days) {
    if (days <= 14) {
      return StatisticsGranularity.day;
    } else if (days <= 30) {
      return StatisticsGranularity.day;
    } else {
      return StatisticsGranularity.day;
    }
  }
}
