import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 登录表单状态
class LoginFormState {
  final String username;
  final String password;
  final bool isLoading;
  final bool agreeToTerms;
  final String? usernameError;
  final String? passwordError;
  final String? generalError;

  const LoginFormState({
    this.username = '',
    this.password = '',
    this.isLoading = false,
    this.agreeToTerms = false,
    this.usernameError,
    this.passwordError,
    this.generalError,
  });

  LoginFormState copyWith({
    String? username,
    String? password,
    bool? isLoading,
    bool? agreeToTerms,
    String? usernameError,
    String? passwordError,
    String? generalError,
  }) {
    return LoginFormState(
      username: username ?? this.username,
      password: password ?? this.password,
      isLoading: isLoading ?? this.isLoading,
      agreeToTerms: agreeToTerms ?? this.agreeToTerms,
      usernameError: usernameError,
      passwordError: passwordError,
      generalError: generalError,
    );
  }

  /// 表单是否有效
  bool get isValid {
    return username.isNotEmpty &&
        password.isNotEmpty &&
        password.length >= 6 &&
        agreeToTerms &&
        usernameError == null &&
        passwordError == null;
  }

  /// 是否可以提交
  bool get canSubmit => isValid && !isLoading;

  @override
  String toString() {
    return 'LoginFormState(username: $username, isValid: $isValid, isLoading: $isLoading, agreeToTerms: $agreeToTerms)';
  }
}

/// 登录表单状态管理器
class LoginFormNotifier extends StateNotifier<LoginFormState> {
  LoginFormNotifier() : super(const LoginFormState());

  /// 更新用户名
  void updateUsername(String username) {
    final error = _validateUsername(username);
    state = state.copyWith(username: username, usernameError: error, generalError: null);
  }

  /// 更新密码
  void updatePassword(String password) {
    final error = _validatePassword(password);
    state = state.copyWith(password: password, passwordError: error, generalError: null);
  }

  /// 更新服务条款同意状态
  void updateAgreeToTerms(bool agree) {
    state = state.copyWith(agreeToTerms: agree, generalError: null);
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  /// 设置通用错误
  void setGeneralError(String? error) {
    state = state.copyWith(generalError: error);
  }

  /// 清除所有错误
  void clearErrors() {
    state = state.copyWith(usernameError: null, passwordError: null, generalError: null);
  }

  /// 重置表单
  void reset() {
    state = const LoginFormState();
  }

  /// 验证用户名
  String? _validateUsername(String username) {
    if (username.isEmpty) {
      return '请输入用户名';
    }
    if (username.length < 3) {
      return '用户名长度不能少于3位';
    }
    if (username.length > 20) {
      return '用户名长度不能超过20位';
    }
    // 检查用户名格式（字母、数字、下划线）
    final regex = RegExp(r'^[a-zA-Z0-9_]+$');
    if (!regex.hasMatch(username)) {
      return '用户名只能包含字母、数字和下划线';
    }
    return null;
  }

  /// 验证密码
  String? _validatePassword(String password) {
    if (password.isEmpty) {
      return '请输入密码';
    }
    if (password.length < 6) {
      return '密码长度不能少于6位';
    }
    if (password.length > 20) {
      return '密码长度不能超过20位';
    }
    return null;
  }

  /// 验证表单
  bool validateForm() {
    final usernameError = _validateUsername(state.username);
    final passwordError = _validatePassword(state.password);

    state = state.copyWith(usernameError: usernameError, passwordError: passwordError);

    if (!state.agreeToTerms) {
      state = state.copyWith(generalError: '请同意服务协议和隐私政策');
      return false;
    }

    return usernameError == null && passwordError == null;
  }
}

/// 登录表单Provider
final loginFormProvider = StateNotifierProvider<LoginFormNotifier, LoginFormState>((ref) {
  return LoginFormNotifier();
});
