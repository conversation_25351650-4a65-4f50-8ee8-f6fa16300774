import 'dart:io';
import 'dart:ui';

import 'package:chickafocus/providers/app_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum WorkingState { ready, working }

enum TimerMode { countdown, stopwatch }

/// 主屏幕页面
///
/// 包含地图、计时选择两个主要的页面，
/// 由于内部存在重复元素和动画逻辑，
/// 所以将其抽象为一个单独的页面组件。
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  /// 是否加载了地图
  bool _isMapLoaded = false;

  /// 当前计时器模式
  TimerMode _timerMode = TimerMode.countdown;

  /// 计时器的分钟数
  int _minutes = 0;

  // 驱动动画的状态
  WorkingState _currentState = WorkingState.ready;
  final _animationDurationNumber = 700;
  late final _animationDuration = Duration(milliseconds: _animationDurationNumber);
  // Webview 控制器
  InAppWebViewController? webViewController;

  /// 北京经纬度
  final List<double> _beijingPosition = [116.4074, 39.9042];

  /// 上海经纬度
  final List<double> _shanghaiPosition = [121.4737, 31.2304];

  final InAppLocalhostServer localhostServer = InAppLocalhostServer(
    documentRoot: 'assets/html/map',
  );

  /// 切换状态
  void _toggleState() {
    setState(() {
      _currentState = _currentState == WorkingState.ready
          ? WorkingState.working
          : WorkingState.ready;
    });
    HapticFeedback.selectionClick();
  }

  (double, double, double) getPostionValues() {
    // 根据屏幕方向返回定位值
    // 竖屏状态下定位到底部中心
    // 横屏状态下定位到右下角
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (16, 32, 16);
    } else {
      final screenSize = MediaQuery.of(context).size;
      return (screenSize.height - 32, 0, 16);
    }
  }

  Future<void> _mapSetZoomAndCenter() async {
    if (webViewController != null) {
      await webViewController!.evaluateJavascript(
        source:
            'setZoomAndCenter(7, [${_beijingPosition[0]}, ${_beijingPosition[1]}], false, $_animationDurationNumber);',
      );
    }
  }

  Future<void> _addCircleMarker(double lng, double lat, double radius) async {
    if (webViewController != null) {
      final source = 'addMarker($lng, $lat, $radius);';
      debugPrint('Adding circle marker with source: $source');
      await webViewController!.evaluateJavascript(source: source);
    }
  }

  Future<void> _addConnection() async {
    if (webViewController != null) {
      final source = 'drawAnimatedConnection([116.4074, 39.9042], [121.4737, 31.2304]);';
      debugPrint('Adding line with source: $source');
      await webViewController!.evaluateJavascript(source: source);
    }
  }

  Future<void> _fitView() async {
    if (webViewController != null) {
      await webViewController!.evaluateJavascript(source: 'fitView();');
    }
  }

  @override
  void initState() {
    super.initState();
    // 启动本地服务器
    localhostServer.start();
  }

  @override
  void dispose() {
    // 停止本地服务器
    localhostServer.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final (pl, pb, pr) = getPostionValues();
    final screen = MediaQuery.of(context).size;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    const titleTextSize = 32.0;
    const textStyle = TextStyle(
      fontSize: titleTextSize,
      color: Colors.black,
      fontWeight: FontWeight.bold,
    );
    final mainBtnWidth = 172.0;

    return Material(
      child: Stack(
        children: [
          AnimatedOpacity(
            opacity: _isMapLoaded ? 1.0 : 0.0,
            curve: Curves.easeInOut,
            duration: _animationDuration,
            child: InAppWebView(
              initialSettings: InAppWebViewSettings(isInspectable: kDebugMode),
              initialUrlRequest: URLRequest(
                url: WebUri("http://localhost:8080/index.html"),
              ),
              onWebViewCreated: (controller) {
                webViewController = controller;
                debugPrint('WebView created');
              },
              onLoadStart: (controller, url) {
                debugPrint('WebView started loading: $url');
              },
              onLoadStop: (controller, url) async {
                debugPrint('WebView stopped loading: $url');
                // 添加两个城市的点
                await Future.wait([
                  _addCircleMarker(_beijingPosition[0], _beijingPosition[1], 48.0),
                ]);
                // 适应视图
                await _fitView();
                // 刷新 UI
                if (mounted) {
                  setState(() {
                    debugPrint('_isMapLoaded set to true');
                    _isMapLoaded = true;
                  });
                }
              },
              onConsoleMessage: (controller, consoleMessage) {
                debugPrint('WebView console message: ${consoleMessage.message}');
              },
            ),
          ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: statusBarHeight + 128,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.white, Colors.white.withValues(alpha: 0)],
                ),
              ),
              padding: EdgeInsets.only(top: statusBarHeight + 32, left: 32, right: 32),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AnimatedOpacity(
                    opacity: _currentState == WorkingState.working ? 0.0 : 1.0,
                    duration: _animationDuration,
                    child: Text(
                      '正在进行旅程',
                      style: textStyle.copyWith(fontSize: titleTextSize * 0.75),
                    ),
                  ),
                  AnimatedOpacity(
                    opacity: _currentState == WorkingState.working ? 0.0 : 1.0,
                    duration: _animationDuration,
                    child: const Text('北京—上海', style: textStyle),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: statusBarHeight,
            left: 0,
            right: 0,
            child: Center(
              child: AnimatedOpacity(
                opacity: _currentState == WorkingState.working ? 1.0 : 0.0,
                duration: _animationDuration,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TabBarItem(
                      isSelected: _timerMode == TimerMode.countdown,
                      text: '番茄钟',
                      onTap: () {
                        setState(() {
                          _timerMode = TimerMode.countdown;
                        });
                      },
                    ),
                    TabBarItem(
                      isSelected: _timerMode == TimerMode.stopwatch,
                      text: '正计时',
                      onTap: () {
                        setState(() {
                          _timerMode = TimerMode.stopwatch;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            top: statusBarHeight + 10,
            left: 24,
            child: Center(
              child: AnimatedOpacity(
                opacity: _currentState == WorkingState.working ? 1.0 : 0.0,
                duration: _animationDuration,
                child: GestureDetector(
                  onTap: () {
                    ref.read(appProvider.notifier).toggleBottomNavigationBarVisibility();
                    _mapSetZoomAndCenter();
                    _toggleState();
                  },
                  child: const Icon(
                    Icons.arrow_back_ios_new,
                    color: Colors.black,
                    size: 24.0,
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Center(
              child: AnimatedOpacity(
                opacity: _currentState == WorkingState.working ? 1.0 : 0.0,
                duration: _animationDuration,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.white.withValues(alpha: 0),
                        Colors.white.withValues(alpha: 0.7),
                        Colors.white,
                      ],
                    ),
                  ),
                  width: double.infinity,
                  height: 128,
                  alignment: Alignment.center,
                  child: Text(
                    '北京—上海',
                    style: textStyle.copyWith(
                      fontSize: 24.0,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
          AnimatedPositioned(
            duration: _animationDuration,
            curve: Curves.easeInOut,
            bottom: pb + 96,
            right: _currentState == WorkingState.working
                ? (screen.width - mainBtnWidth) / 2
                : 16,
            child: GestureDetector(
              onTap: () async {
                switch (_currentState) {
                  case WorkingState.ready:
                    ref.read(appProvider.notifier).toggleBottomNavigationBarVisibility();
                    // _mapSetZoomAndCenter();
                    await _addCircleMarker(
                      _shanghaiPosition[0],
                      _shanghaiPosition[1],
                      48.0,
                    );
                    await _fitView();
                    _addConnection();
                    _toggleState();
                    break;
                  case WorkingState.working:
                    break;
                }
              },
              child: Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(32.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      offset: const Offset(0, 2),
                      blurRadius: 4.0,
                    ),
                  ],
                ),
                height: 54,
                width: mainBtnWidth,
                child: const Text(
                  '开始旅程',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            right: 0,
            top: (screen.height - 288 - statusBarHeight) / 2,
            child: AnimatedOpacity(
              opacity:
                  _currentState == WorkingState.working &&
                      _timerMode == TimerMode.countdown
                  ? 1.0
                  : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(32.0)),
                width: 108,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(32.0),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 1.0, sigmaY: 1.0),
                    child: Container(
                      decoration: BoxDecoration(
                        // color: Colors.white.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(32.0),
                      ),
                      padding: const EdgeInsets.only(
                        left: 16.0,
                        right: 16.0,
                        top: 32.0,
                        bottom: 32.0,
                      ),
                      child: Column(
                        children: [
                          TimerSlider(
                            initialValue: _minutes,
                            minMinutes: 5,
                            maxMinutes: 60,
                            height: 288,
                            onChanged: (newValue) {
                              setState(() {
                                _minutes = newValue;
                              });
                            },
                          ),
                          const SizedBox(height: 16.0),
                          Text(
                            '$_minutes分钟',
                            style: const TextStyle(
                              fontSize: 20.0,
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 顶部导航栏的Tab项组件
/// 用于显示不同的计时模式
///
/// 每个Tab项包含一个文本和一个指示器
/// 当选中时，指示器会显示出来
///
/// 每个Tab项可以通过onTap回调来切换计时模式
///
/// - isSelected: 是否选中
/// - text: 显示的文本
/// - onTap: 点击回调
class TabBarItem extends StatelessWidget {
  final bool isSelected;
  final String text;
  final VoidCallback? onTap;

  const TabBarItem({super.key, required this.isSelected, required this.text, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
        child: Column(
          children: [
            Text(
              text,
              style: TextStyle(
                color: Colors.black,
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
                shadows: isSelected
                    ? [const Shadow(color: Colors.black26, blurRadius: 2.0)]
                    : [],
              ),
            ),
            AnimatedOpacity(
              opacity: isSelected ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 180),
              child: Container(
                margin: const EdgeInsets.only(top: 4.0),
                width: 32.0,
                height: 4.0,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(2.0),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TimerSlider extends StatefulWidget {
  final int initialValue;
  final int minMinutes;
  final int maxMinutes;
  final ValueChanged<int> onChanged;
  final double height;
  final double itemHeight;

  const TimerSlider({
    super.key,
    required this.initialValue,
    required this.onChanged,
    this.minMinutes = 5,
    this.maxMinutes = 60,
    this.height = 300,
    this.itemHeight = 16,
  });

  @override
  _TimerSliderState createState() => _TimerSliderState();
}

class _TimerSliderState extends State<TimerSlider> {
  late ScrollController _scrollController;
  int _currentValue = 0;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initialValue;
    final initialOffset = (widget.initialValue - widget.minMinutes) * widget.itemHeight;
    _scrollController = ScrollController(initialScrollOffset: initialOffset);

    _scrollController.addListener(_onScroll);

    // 延迟添加滚动状态监听器，确保 ScrollController 已经附加到滚动视图
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.addListener(_onScroll);
      _scrollController.position.isScrollingNotifier.addListener(() {
        if (!_scrollController.position.isScrollingNotifier.value && _isDragging) {
          HapticFeedback.heavyImpact();
          _isDragging = false;
        }
      });
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (!_isDragging) {
      _isDragging = true;
    }
    // 获取当前滚动偏移量
    final offset = _scrollController.offset;
    // 计算当前中心位置对应的分钟数
    // 我们需要加上一半的滚动容器高度，再减去一半的 item 高度，
    // 这样才能精确地将中心线对齐到正确的 item
    final centerOffset = offset + widget.height / 2 - widget.itemHeight / 2;
    final newValueIndex = (centerOffset / widget.itemHeight).round();
    final newValue = newValueIndex + widget.minMinutes;

    if (newValue != _currentValue) {
      // 检查新值是否在有效范围内
      final clampedValue = newValue.clamp(widget.minMinutes, widget.maxMinutes);
      if (clampedValue != _currentValue) {
        setState(() {
          _currentValue = clampedValue;
        });
        HapticFeedback.lightImpact();
        widget.onChanged(_currentValue);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 在列表顶部和底部添加空白，以确保第一个和最后一个元素可以滚动到中心
    // 计算顶部和底部需要添加的空白高度，使第一个和最后一个元素可以滚动到中心
    final emptySpace = widget.height / 2 - widget.itemHeight / 2;

    return SizedBox(
      height: widget.height,
      width: 52,
      child: ListView.builder(
        controller: _scrollController,
        itemExtent: widget.itemHeight,
        itemCount: widget.maxMinutes - widget.minMinutes + 1,
        padding: EdgeInsets.symmetric(vertical: emptySpace), // 在这里添加内边距
        itemBuilder: (context, index) {
          final minute = index + widget.minMinutes;
          return Center(
            child: MinuteLine(
              currentValue: _currentValue,
              isBigTick: minute % 5 == 0,
              itemHeight: widget.itemHeight,
            ),
          );
        },
      ),
    );
  }
}

class MinuteLine extends StatelessWidget {
  final int currentValue;
  final bool isBigTick;
  final double itemHeight;

  const MinuteLine({
    super.key,
    required this.currentValue,
    required this.isBigTick,
    required this.itemHeight,
  });

  @override
  Widget build(BuildContext context) {
    final width = isBigTick ? 45.0 : 30.0;
    final height = isBigTick ? 5.0 : 3.0;
    final color = Colors.black;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [Container(width: width, height: height, color: color)],
    );
  }
}
