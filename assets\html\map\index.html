<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="viewport"
      content="initial-scale=1.0, user-scalable=no, width=device-width"
    />
    <title>AMAP</title>
    <style>
      html,
      body,
      #container {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
      }
    </style>
    <script type="text/javascript">
      window._AMapSecurityConfig = {
        securityJsCode: "4ef657a379f13efbbf096baf8b08b3ed",
      };
    </script>
    <script src="https://webapi.amap.com/loader.js"></script>
  </head>
  <body>
    <div id="container"></div>

    <script type="text/javascript">
      // 强制使用 WebGL 渲染
      window.forceWebGL = true;
      var map;
      var mapLoaded = false;
      var canvasSize = 256;
      var markers = [];

      // 异步加载高德地图并初始化
      AMapLoader.load({
        key: "82ea7ca3d47546f079185e7ccdade9ba",
        version: "2.0",
      })
        .then((AMap) => {
          // 初始化地图
          map = new AMap.Map("container", {
            zoom: 6,
            mapStyle: "amap://styles/8f622d6dab790c2beaafeaa2590074e4",
            scrollWheel: false,
            doubleClickZoom: false,
            dragEnable: false,
            keyboardEnable: false,
            touchZoom: false,
            dragEnable: false,
          });
          console.log("高德地图初始化完成");
          mapLoaded = true;

          if (window.onMapReady) {
            window.onMapReady();
          }
        })
        .catch((e) => {
          console.error("地图加载失败：", e);
        });

      // 外部调用的函数，用于安全地设置地图缩放和中心点
      function setZoomAndCenter(zoom, center, immediately, duration) {
        if (!mapLoaded) {
          console.error("地图尚未初始化，无法调用 setZoomAndCenter");
          return;
        }
        map.setZoomAndCenter(zoom, center, immediately, duration);
      }

      function addMarker(lng, lat, radius) {
        // 对 lng 和 lat 取整数位后拼接作为 canvas 名称
        var canvasName = "canvas_" + Math.floor(lng) + "_" + Math.floor(lat);
        // var canvasName = "canvas";
        var canvas = document.createElement("canvas");
        canvas.id = canvasName;
        canvas.width = canvasSize;
        canvas.height = canvasSize;
        var context = canvas.getContext("2d");

        // 动画状态变量
        var frameCount = 0;
        var pulses = []; // 存储所有扩散中的脉冲
        var maxRadius = canvasSize / 2; // 最大半径，默认为 canvasSize/2
        var pulseInterval = 90; // 每隔 90 帧生成一个新脉冲

        // 绘制函数
        var draw = function () {
          // 每一帧都清空画布，使其背景透明
          context.clearRect(0, 0, canvas.width, canvas.height);

          // 移除已完成的脉冲
          pulses = pulses.filter((p) => p.progress < 1);

          // 更新和绘制每个脉冲
          pulses.forEach((pulse) => {
            // 线性更新脉冲的进度
            pulse.progress += 0.01;

            // 根据进度计算当前半径和透明度
            const currentRadius = maxRadius * pulse.progress;
            // 透明度从 1 线性递减到 0
            const currentAlpha = 1 - pulse.progress;

            // 设置绘制样式
            context.beginPath();
            context.arc(
              canvasSize / 2,
              canvasSize / 2,
              currentRadius,
              0,
              2 * Math.PI
            );
            context.strokeStyle = "rgba(0, 0, 0, " + currentAlpha + ")";
            context.lineWidth = currentRadius / 8;
            context.stroke();
          });

          // 绘制中心永久存在的白色圆点
          // 保存当前绘图状态
          context.save();

          // 设置阴影效果
          context.shadowColor = "rgba(64, 64, 64, 0.2)"; // 阴影颜色：半透明灰色
          context.shadowBlur = 8; // 阴影模糊程度
          context.shadowOffsetX = 2; // 阴影水平偏移
          context.shadowOffsetY = 2; // 阴影垂直偏移

          // 绘制中心点
          context.beginPath();
          context.arc(canvasSize / 2, canvasSize / 2, radius, 0, 2 * Math.PI);
          context.fillStyle = "#FDFDFD";
          context.fill();
          context.strokeStyle = "#070707";
          context.lineWidth = radius / 4;
          context.stroke();

          // 恢复绘图状态，清除阴影设置
          context.restore();

          // 每隔 pulseInterval 帧生成一个新脉冲
          frameCount++;
          if (frameCount % pulseInterval === 0) {
            pulses.push({
              progress: 0,
            });
          }

          // 请求下一帧动画
          AMap.Util.requestAnimFrame(draw);
        };

        var loc = calculateBounds(lat, lng, 196);
        console.log("计算出的西南角和东北角经纬度：", loc);

        // 创建 Canvas 图层
        var CanvasLayer = new AMap.CanvasLayer({
          canvas: canvas,
          // 将 bounds 设置为一个近似正方形的地理范围，避免拉伸
          // 以中心点为基准，向四周扩展，确保经纬度范围近似相等
          bounds: new AMap.Bounds(
            [loc.southwest[0], loc.southwest[1]],
            [loc.northeast[0], loc.northeast[1]]
          ),
          zooms: [3, 18],
        });

        // 将 Canvas 图层添加到地图
        CanvasLayer.setzIndex(100);
        map.addLayer(CanvasLayer);

        var circleMarker = new AMap.CircleMarker({
          center: [lng, lat],
          radius: 0,
          strokeColor: "black",
          strokeWeight: 2,
          strokeOpacity: 0,
          fillColor: "rgba(233,233,233,0)",
          fillOpacity: 0,
          zIndex: 10,
          cursor: "pointer",
        });
        markers.push(circleMarker);

        draw();
      }

      // 外部调用的函数，用于自适应视图
      function fitView() {
        if (!mapLoaded) {
          console.error("地图尚未初始化，无法调用 fitView");
          return;
        }
        console.log("自适应视图");
        // 计算所有 markers 的中心点
        if (markers.length === 0) {
          console.warn("没有可用的标记，无法自适应视图");
          return;
        }
        console.log("markers 数量：", markers.length);

        var lngSum = 0;
        var latSum = 0;
        markers.forEach((marker) => {
          var center = marker.getCenter();
          lngSum += center.lng;
          latSum += center.lat;
        });
        var centerLng = lngSum / markers.length;
        var centerLat = latSum / markers.length;

        // 设置地图中心点和缩放级别
        map.setZoomAndCenter(5, [centerLng, centerLat], false, 700);
      }

      /**
       * 根据中心点经纬度和一个方形地理距离，计算出西南角和东北角的经纬度。
       * * @param {number} centerLat 中心点的纬度。
       * @param {number} centerLon 中心点的经度。
       * @param {number} distanceKm 以公里为单位的方形区域边长。
       * @returns {{
       * southwest: [number, number],
       * northeast: [number, number]
       * }} 返回一个包含西南角和东北角经纬度的对象。
       */
      function calculateBounds(centerLat, centerLon, distanceKm) {
        // 地球的平均半径，单位：公里
        const R = 6371;

        // 将距离转换为弧度
        const latRadianOffset = distanceKm / 2 / R;

        // 纬度每度的近似距离 (约 111.32 km/degree)
        const latDegreeOffset = distanceKm / 111.32;

        // 经度每度的距离会随纬度变化，这里计算经度偏移
        const lonDegreeOffset =
          distanceKm /
          2 /
          ((R * Math.cos((centerLat * Math.PI) / 180) * Math.PI) / 180);

        // 计算西南角经纬度
        const southwestLat = centerLat - latDegreeOffset / 2;
        const southwestLon = centerLon - lonDegreeOffset;

        // 计算东北角经纬度
        const northeastLat = centerLat + latDegreeOffset / 2;
        const northeastLon = centerLon + lonDegreeOffset;

        return {
          southwest: [southwestLon, southwestLat],
          northeast: [northeastLon, northeastLat],
        };
      }

      // --- 绘制动态连接线的主函数 ---
      /**
       * 绘制两地理坐标点之间的动态贝塞尔曲线
       * @param {number[]} startPoint [lon, lat] 起点经纬度
       * @param {number[]} endPoint [lon, lat] 终点经纬度
       */
      function drawAnimatedConnection(startPoint, endPoint) {
        // 确定Canvas的尺寸
        const canvasWidth = 512;
        const canvasHeight = 512;

        // 创建一个Canvas元素
        const canvas = document.createElement("canvas");
        canvas.width = canvasWidth;
        canvas.height = canvasHeight;
        const context = canvas.getContext("2d");

        // 根据起点和终点计算Canvas图层的地理边界
        // 这里简单地取两点的最小/最大经纬度，并添加一些缓冲
        const minLon = Math.min(startPoint[0], endPoint[0]);
        const maxLon = Math.max(startPoint[0], endPoint[0]);
        const minLat = Math.min(startPoint[1], endPoint[1]);
        const maxLat = Math.max(startPoint[1], endPoint[1]);

        const bounds = new AMap.Bounds(
          [minLon - 0.5, minLat - 0.5],
          [maxLon + 0.5, maxLat + 0.5]
        );

        // 将地理坐标转换为Canvas上的像素坐标
        function geoToPixel(lon, lat) {
          const lonRange =
            bounds.getNorthEast().getLng() - bounds.getSouthWest().getLng();
          const latRange =
            bounds.getNorthEast().getLat() - bounds.getSouthWest().getLat();

          const x =
            ((lon - bounds.getSouthWest().getLng()) / lonRange) * canvasWidth;
          // Canvas的y轴方向与地图的纬度方向相反，所以需要反转
          const y =
            (1 - (lat - bounds.getSouthWest().getLat()) / latRange) *
            canvasHeight;
          return { x, y };
        }

        // 计算贝塞尔曲线的控制点，使其形成一个较低的对称弧线
        function getControlPoint() {
          const startPixel = geoToPixel(startPoint[0], startPoint[1]);
          const endPixel = geoToPixel(endPoint[0], endPoint[1]);
          const midPointX = (startPixel.x + endPixel.x) / 2;
          const midPointY = (startPixel.y + endPixel.y) / 2;

          // 垂直于起点到终点的连线，并添加一个较小的偏移量来控制弧高
          const offsetX = (endPixel.y - startPixel.y) * 0.4;
          const offsetY = (startPixel.x - endPixel.x) * 0.4;

          return {
            x: midPointX + offsetX,
            y: midPointY + offsetY,
          };
        }

        const controlPoint = getControlPoint();
        let dashOffset = 0; // 虚线动画的偏移量
        const dashLength = 20; // 虚线段的长度

        // 动画绘制函数
        function animate() {
          // 清空Canvas，使其背景透明
          context.clearRect(0, 0, canvasWidth, canvasHeight);

          const startPixel = geoToPixel(startPoint[0], startPoint[1]);
          const endPixel = geoToPixel(endPoint[0], endPoint[1]);

          // 绘制二次贝塞尔曲线
          context.beginPath();
          context.moveTo(startPixel.x, startPixel.y);
          context.quadraticCurveTo(
            controlPoint.x,
            controlPoint.y,
            endPixel.x,
            endPixel.y
          );

          // 设置虚线样式
          context.strokeStyle = "#000000"; // 黑色虚线
          context.lineWidth = 8;
          context.setLineDash([dashLength, dashLength]);
          context.lineDashOffset = -dashOffset;
          context.stroke();

          // 更新虚线偏移量以实现动画
          dashOffset += 1;
          if (dashOffset >= dashLength * 2) {
            dashOffset = 0;
          }

          // 请求下一帧动画
          AMap.Util.requestAnimFrame(animate);
        }

        // 创建Canvas图层并添加到地图
        const CanvasLayer = new AMap.CanvasLayer({
          canvas: canvas,
          bounds: bounds,
          zooms: [3, 18],
        });
        map.addLayer(CanvasLayer);

        // 开始动画循环
        animate();
      }
    </script>
  </body>
</html>
