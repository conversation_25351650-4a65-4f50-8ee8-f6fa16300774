import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_ce/hive.dart';
import '../models/user_model.dart';

/// 本地存储服务
/// - 使用 FlutterSecureStorage 安全存储 token
/// - 使用 Hive 存储用户信息
class UserStorageService {
  static const String _tokenKey = 'auth_token';
  static const String _userBoxName = 'userBox';
  static const String _userKey = 'currentUser';

  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  // 读取 Hive Box
  Box get _userBox => Hive.box(_userBoxName);

  // Token
  Future<void> saveToken(String token) async {
    await _secureStorage.write(key: _tokenKey, value: token);
  }

  Future<String?> readToken() async {
    return await _secureStorage.read(key: _tokenKey);
  }

  Future<void> deleteToken() async {
    await _secureStorage.delete(key: _tokenKey);
  }

  // User
  Future<void> saveUser(UserModel user) async {
    await _userBox.put(_userKey, jsonEncode(user.toJson()));
  }

  UserModel? _readUserSync() {
    final raw = _userBox.get(_userKey) as String?;
    if (raw == null) return null;
    try {
      final map = jsonDecode(raw) as Map<String, dynamic>;
      return UserModel.fromJson(map);
    } catch (_) {
      return null;
    }
  }

  // 当前为同步读的异步包装
  Future<UserModel?> readUser() async {
    return _readUserSync();
  }

  Future<void> deleteUser() async {
    await _userBox.delete(_userKey);
  }
}
