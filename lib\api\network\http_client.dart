import 'package:dio/dio.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// HTTP客户端配置类
///
/// 包含认证拦截器、错误处理和统一响应格式处理
class HttpClient {
  static const String baseUrl = 'http://47.245.40.222:8199/api/v1'; // 根据实际API地址修改
  static const Duration connectTimeout = Duration(seconds: 10);
  static const Duration receiveTimeout = Duration(seconds: 10);
  static const Duration sendTimeout = Duration(seconds: 30);

  late final Dio _dio;
  String? _token;

  HttpClient() {
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: connectTimeout,
        receiveTimeout: receiveTimeout,
        sendTimeout: sendTimeout,
        headers: {'Content-Type': 'application/json', 'Accept': 'application/json'},
      ),
    );

    _setupInterceptors();
  }

  /// 设置拦截器
  void _setupInterceptors() {
    // 请求拦截器 - 添加认证头
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          if (_token != null) {
            options.headers['Authorization'] = 'Bearer $_token';
          }
          handler.next(options);
        },
        onResponse: (response, handler) {
          // 统一响应处理
          final data = response.data;
          if (data is Map<String, dynamic>) {
            final code = data['code'] as int?;
            final message = data['message'] as String?;

            // 根据业务状态码处理（兼容后端 code=0 或 code=200 为成功）
            if (code != null && code != 0 && code != 200) {
              throw DioException(
                requestOptions: response.requestOptions,
                response: response,
                type: DioExceptionType.badResponse,
                message: message ?? '请求失败',
              );
            }
          }
          handler.next(response);
        },
        onError: (error, handler) {
          // 统一错误处理
          if (error.response?.statusCode == 401) {
            // Token过期，清除本地token
            _token = null;
          }
          handler.next(error);
        },
      ),
    );

    // 日志拦截器（仅在调试模式下）
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => debugPrint(obj.toString()),
      ),
    );
  }

  /// 设置认证token
  void setToken(String? token) {
    _token = token;
  }

  /// 获取当前token
  String? get token => _token;

  /// 清除token
  void clearToken() {
    _token = null;
  }

  /// GET请求
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.get<T>(path, queryParameters: queryParameters, options: options);
  }

  /// POST请求
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    return await _dio.post<T>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: options,
    );
  }
}

/// HTTP客户端Provider
final httpClientProvider = Provider<HttpClient>((ref) {
  return HttpClient();
});
