/// 旅程统计数据模型
class JourneyStatisticsModel {
  final String date;
  final String value;

  const JourneyStatisticsModel({
    required this.date,
    required this.value,
  });

  /// 从JSON创建模型
  factory JourneyStatisticsModel.fromJson(Map<String, dynamic> json) {
    return JourneyStatisticsModel(
      date: json['date'] as String,
      value: json['value'] as String,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'value': value,
    };
  }

  /// 获取数值形式的value
  double get numericValue {
    try {
      return double.parse(value);
    } catch (e) {
      return 0.0;
    }
  }

  /// 获取日期时间对象
  DateTime? get dateTime {
    try {
      return DateTime.parse(date);
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'JourneyStatisticsModel(date: $date, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JourneyStatisticsModel &&
        other.date == date &&
        other.value == value;
  }

  @override
  int get hashCode => date.hashCode ^ value.hashCode;
}

/// 旅程统计请求参数
class JourneyStatisticsRequest {
  final String uid;
  final String startDate;
  final String? endDate;
  final String granularity;

  const JourneyStatisticsRequest({
    required this.uid,
    required this.startDate,
    this.endDate,
    required this.granularity,
  });

  /// 转换为查询参数
  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{
      'uid': uid,
      'start_date': startDate,
      'granularity': granularity,
    };
    
    if (endDate != null) {
      params['end_date'] = endDate;
    }
    
    return params;
  }

  @override
  String toString() {
    return 'JourneyStatisticsRequest(uid: $uid, startDate: $startDate, endDate: $endDate, granularity: $granularity)';
  }
}

/// 统计粒度枚举
enum StatisticsGranularity {
  day('day'),
  week('week'), 
  month('month');

  const StatisticsGranularity(this.value);
  final String value;

  static StatisticsGranularity fromString(String value) {
    switch (value) {
      case 'day':
        return StatisticsGranularity.day;
      case 'week':
        return StatisticsGranularity.week;
      case 'month':
        return StatisticsGranularity.month;
      default:
        throw ArgumentError('Invalid granularity: $value');
    }
  }
}