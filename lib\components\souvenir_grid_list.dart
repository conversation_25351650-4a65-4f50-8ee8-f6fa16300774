import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/souvenir/souvenir_item.dart';
import '../models/souvenir/category_enum.dart';
import '../providers/souvenir_provider.dart';
import 'category_tabs.dart';

/// 奖励网格列表组件
///
/// 展示三列网格布局的奖励列表
/// 支持下拉刷新和上拉加载更多
class SouvenirGridList extends ConsumerStatefulWidget {
  const SouvenirGridList({super.key});

  @override
  ConsumerState<SouvenirGridList> createState() => _SouvenirGridListState();
}

class _SouvenirGridListState extends ConsumerState<SouvenirGridList> {
  final ScrollController _scrollController = ScrollController();
  static final _horizontalSpace = 8.0; // 水平间距，padding 为该值的两倍
  static final _aspectRatio = 0.9; // 宽高比，调整卡片形状

  @override
  void initState() {
    super.initState();
    // 监听滚动事件，实现上拉加载
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 滚动监听方法
  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 400) {
      // 距离底部400px时开始加载更多
      final selectedCategory = ref.read(selectedCategoryProvider);
      final query = SouvenirQuery(
        category: selectedCategory == ItemCategory.all
            ? null
            : selectedCategory.value,
      );
      ref.read(souvenirListProvider(query).notifier).loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedCategory = ref.watch(selectedCategoryProvider);
    final query = SouvenirQuery(
      category: selectedCategory == ItemCategory.all
          ? null
          : selectedCategory.value,
    );
    final souvenirState = ref.watch(souvenirListProvider(query));

    // 监听分类变化并重新加载数据
    ref.listen(selectedCategoryProvider, (prev, next) {
      if (prev != next) {
        final newQuery = SouvenirQuery(
          category: next == ItemCategory.all ? null : next.value,
        );
        ref.read(souvenirListProvider(newQuery).notifier).refresh();
      }
    });

    // 错误状态
    if (souvenirState.error != null && souvenirState.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              souvenirState.error!,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(souvenirListProvider(query).notifier).refresh();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    // 初始加载状态
    if (souvenirState.isLoading && souvenirState.items.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: DotsLoadingIndicator(dotSize: 10, spacing: 8),
        ),
      );
    }

    // 空数据状态
    if (souvenirState.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.card_giftcard_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text('暂无奖励数据', style: TextStyle(color: Colors.grey[600])),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(souvenirListProvider(query).notifier).refresh();
      },
      child: GridView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(_horizontalSpace * 2),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3, // 三列布局
          crossAxisSpacing: _horizontalSpace, // 列间距
          mainAxisSpacing: 16, // 行间距
          childAspectRatio: _aspectRatio, // 宽高比，调整卡片形状
        ),
        itemCount:
            souvenirState.items.length + (souvenirState.isLoading ? 1 : 0),
        itemBuilder: (context, index) {
          // 加载更多指示器
          if (index == souvenirState.items.length) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: DotsLoadingIndicator(dotSize: 6, spacing: 6),
              ),
            );
          }

          final item = souvenirState.items[index];
          return SouvenirCard(item: item);
        },
      ),
    );
  }
}

/// 单个奖励卡片组件
class SouvenirCard extends StatelessWidget {
  final SouvenirItem item;

  const SouvenirCard({super.key, required this.item});

  /// 构建分类标签
  /// 根据分类类型返回不同的标签样式
  Widget? _buildCategoryTag(ItemCategory category) {
    IconData iconData;
    Color iconColor;
    switch (category) {
      case ItemCategory.postcard:
        iconData = Icons.post_add_outlined;
        iconColor = Colors.blue;
        break;
      case ItemCategory.ticket:
        iconData = Icons.confirmation_number_outlined;
        iconColor = Colors.green;
        break;
      default:
        return null; // 如果没有分类或不需要显示图标，则返回 null
    }
    return Icon(
      iconData,
      size: 20,
      color: iconColor,
      shadows: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.2),
          offset: const Offset(0, 1),
          blurRadius: 2,
          spreadRadius: 0,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 图片区域
        Expanded(
          child: InkWell(
            onTap: () {
              // 展示Toast消息
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    '功能开发中，敬请期待！',
                    style: TextStyle(color: Colors.black87, fontSize: 16),
                  ),
                  duration: const Duration(seconds: 2),
                  behavior: SnackBarBehavior.floating,
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              );
            },
            child: Stack(
              children: [
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white,
                    border: Border.all(color: Colors.black12, width: 1),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.08),
                        offset: const Offset(0, 2),
                        blurRadius: 8,
                        spreadRadius: 0,
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.04),
                        offset: const Offset(0, 1),
                        blurRadius: 2,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10), // 稍微小一点以适应边框
                    child: CachedNetworkImage(
                      imageUrl: item.imgUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: const Color(0xFFF8F8F8),
                        child: Center(
                          child: Icon(
                            Icons.card_giftcard_outlined,
                            size: 32,
                            color: Colors.grey[400],
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: const Color(0xFFF8F8F8),
                        child: Center(
                          child: Icon(
                            Icons.broken_image_outlined,
                            size: 32,
                            color: Colors.grey[400],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // 左上角分类标记
                Positioned(
                  top: 8,
                  left: 8,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    child: _buildCategoryTag(
                      ItemCategory.fromString(item.category),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 4),
        // 标题区域
        Text(
          item.title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF333333),
            height: 1.3,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}

/// 更优雅的“跃动三点”加载动画
class DotsLoadingIndicator extends StatefulWidget {
  final double dotSize;
  final double spacing;
  final Color color;

  const DotsLoadingIndicator({
    super.key,
    this.dotSize = 8,
    this.spacing = 8,
    this.color = const Color(0xFF999999),
  });

  @override
  State<DotsLoadingIndicator> createState() => _DotsLoadingIndicatorState();
}

class _DotsLoadingIndicatorState extends State<DotsLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 1200),
  )..repeat();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> dots = List.generate(3, (i) {
      final start = i * 0.2;
      final end = start + 0.6;
      final animation = CurvedAnimation(
        parent: _controller,
        curve: Interval(start, end, curve: Curves.easeInOut),
      );

      return AnimatedBuilder(
        animation: animation,
        builder: (context, child) {
          // 0.6~1.0 的缩放，配合透明度，形成柔和呼吸效果
          final scale = 0.6 + 0.4 * (1 - (animation.value - 0.5).abs() * 2);
          final opacity = 0.5 + 0.5 * scale;
          return Opacity(
            opacity: opacity,
            child: Transform.scale(
              scale: scale,
              child: Container(
                width: widget.dotSize,
                height: widget.dotSize,
                decoration: BoxDecoration(
                  color: widget.color,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          );
        },
      );
    });

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (int i = 0; i < dots.length; i++) ...[
          dots[i],
          if (i < dots.length - 1) SizedBox(width: widget.spacing),
        ],
      ],
    );
  }
}
