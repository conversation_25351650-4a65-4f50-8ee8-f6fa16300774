import 'package:chickafocus/pages/main_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../components/auth/custom_text_field.dart';
import '../components/auth/social_login_button.dart';
import '../providers/login_form_provider.dart';
import '../providers/auth_provider.dart';

/// 登录页面
class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> with TickerProviderStateMixin {
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut));
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));

    // 启动动画
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });

    // 监听输入变化
    _usernameController.addListener(_onUsernameChanged);
    _passwordController.addListener(_onPasswordChanged);
  }

  @override
  void dispose() {
    _usernameController.removeListener(_onUsernameChanged);
    _passwordController.removeListener(_onPasswordChanged);
    _usernameController.dispose();
    _passwordController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _onUsernameChanged() {
    ref.read(loginFormProvider.notifier).updateUsername(_usernameController.text);
  }

  void _onPasswordChanged() {
    ref.read(loginFormProvider.notifier).updatePassword(_passwordController.text);
  }

  @override
  Widget build(BuildContext context) {
    final formState = ref.watch(loginFormProvider);
    final authState = ref.watch(authProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: SingleChildScrollView(
          child: SizedBox(
            height:
                MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top,
            child: Column(
              children: [
                // 背景区域
                _buildBackgroundSection(),

                // 登录表单区域
                Expanded(child: _buildLoginFormSection(formState, authState)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建背景区域
  Widget _buildBackgroundSection() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      width: double.infinity,
      decoration: const BoxDecoration(color: Colors.white),
      child: Stack(
        children: [
          // 背景装饰预留容器
        ],
      ),
    );
  }

  /// 构建登录表单区域
  Widget _buildLoginFormSection(LoginFormState formState, AuthState authState) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24.0),
              topRight: Radius.circular(24.0),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 问候语
                  _buildGreeting(),

                  const SizedBox(height: 32.0),

                  // 登录表单
                  _buildLoginForm(formState),

                  const SizedBox(height: 16.0),

                  // 服务条款
                  _buildTermsAgreement(formState),

                  const SizedBox(height: 24.0),

                  // 登录按钮
                  _buildLoginButton(formState, authState),

                  const SizedBox(height: 32.0),

                  // 第三方登录
                  // _buildSocialLogin(formState),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建问候语
  Widget _buildGreeting() {
    return Row(
      children: [
        const Text('👋', style: TextStyle(fontSize: 24)),
        const SizedBox(width: 8.0),
        Text(
          '你好呀～',
          style: const TextStyle(
            fontSize: 20.0,
            fontWeight: FontWeight.bold,
            color: Color(0xFF212121),
          ),
        ),
      ],
    );
  }

  /// 构建登录表单
  Widget _buildLoginForm(LoginFormState formState) {
    return Column(
      children: [
        CustomTextField(
          controller: _usernameController,
          hintText: '输入用户名',
          prefixIcon: Icons.person_outline,
          errorText: formState.usernameError,
        ),

        const SizedBox(height: 16.0),

        CustomTextField(
          controller: _passwordController,
          hintText: '输入密码',
          prefixIcon: Icons.lock_outline,
          isPassword: true,
          errorText: formState.passwordError,
        ),

        // 显示通用错误信息
        if (formState.generalError != null) ...[
          const SizedBox(height: 8.0),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: const Color(0xFFF44336).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: const Color(0xFFF44336).withValues(alpha: 0.3)),
            ),
            child: Text(
              formState.generalError!,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: Color(0xFFF44336),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 构建服务条款同意
  Widget _buildTermsAgreement(LoginFormState formState) {
    return Row(
      children: [
        Checkbox(
          value: formState.agreeToTerms,
          onChanged: (value) {
            ref.read(loginFormProvider.notifier).updateAgreeToTerms(value ?? false);
          },
          activeColor: const Color(0xFF000000),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w400,
                color: Color(0xFF757575),
              ),
              children: [
                const TextSpan(text: '已同意'),
                WidgetSpan(
                  child: GestureDetector(
                    onTap: () => _openServiceAgreement(),
                    child: const Text(
                      '《服务协议》',
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w400,
                        color: Color.fromARGB(255, 0, 140, 255),
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
                const TextSpan(text: '与'),
                WidgetSpan(
                  child: GestureDetector(
                    onTap: () => _openPrivacyPolicy(),
                    child: const Text(
                      '《隐私政策》',
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFF2196F3),
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建登录按钮
  Widget _buildLoginButton(LoginFormState formState, AuthState authState) {
    final isLoading = formState.isLoading || authState.isLoading;
    final isEnabled = formState.canSubmit && !isLoading;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        color: isEnabled ? Colors.black : Colors.grey.shade300,
        borderRadius: BorderRadius.circular(12),
        boxShadow: isEnabled
            ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  offset: const Offset(0, 4),
                  blurRadius: 12,
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: isEnabled ? _handleLogin : null,
          child: Center(
            child: isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    '登录',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isEnabled ? Colors.white : Colors.grey.shade500,
                    ),
                  ),
          ),
        ),
      ),
    );
  }

  /// 构建第三方登录
  Widget _buildSocialLogin(LoginFormState formState) {
    return SocialLoginButtons(
      onWechatPressed: _handleWechatLogin,
      onQQPressed: _handleQQLogin,
      enabled: !formState.isLoading,
    );
  }

  /// 处理登录
  void _handleLogin() async {
    final formNotifier = ref.read(loginFormProvider.notifier);
    final authNotifier = ref.read(authProvider.notifier);

    // 验证表单
    if (!formNotifier.validateForm()) {
      return;
    }

    final formState = ref.read(loginFormProvider);

    try {
      formNotifier.setLoading(true);

      // 调用自动注册登录
      await authNotifier.loginOrAutoRegister(formState.username, formState.password);

      // 检查登录结果
      final authState = ref.read(authProvider);
      if (authState.isAuthenticated) {
        // 登录成功，导航到主页面
        if (mounted) {
          // AppEntry组件会自动处理导航
        }
      } else if (authState.error != null) {
        // 登录失败，显示错误
        formNotifier.setGeneralError(authState.error);
      }
    } catch (e) {
      formNotifier.setGeneralError('登录失败：${e.toString()}');
    } finally {
      formNotifier.setLoading(false);
    }
  }

  /// 处理微信登录
  void _handleWechatLogin() {}

  /// 处理QQ登录
  void _handleQQLogin() {}

  /// 打开服务协议
  void _openServiceAgreement() async {
    const url = 'https://example.com/service-agreement'; // TODO: 替换为实际的服务协议链接
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showLinkErrorDialog('服务协议');
      }
    } catch (e) {
      _showLinkErrorDialog('服务协议');
    }
  }

  /// 打开隐私政策
  void _openPrivacyPolicy() async {
    const url = 'https://example.com/privacy-policy'; // TODO: 替换为实际的隐私政策链接
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showLinkErrorDialog('隐私政策');
      }
    } catch (e) {
      _showLinkErrorDialog('隐私政策');
    }
  }

  /// 显示链接打开失败对话框
  void _showLinkErrorDialog(String linkName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text(
          '无法打开链接',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        content: Text(
          '无法打开$linkName页面，请检查网络连接或稍后重试。',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              '确定',
              style: TextStyle(
                fontSize: 16,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
